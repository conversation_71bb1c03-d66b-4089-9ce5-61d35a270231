import re
import unicodedata
from typing import Optional
import cv2
import pandas as pd

from OCR.OcrEngine import OcrEngine
from OCR.OcrPreview import OcrPreviewQt


class OcrPipeline:
    def __init__(self, lang: str = "ces+eng"):
        self.engine = OcrEngine(lang)
        self.image = None

    def load_image(self, image: cv2.Mat) -> None:
        self.image = image

    def process(self, image: Optional [cv2.Mat]) -> pd.DataFrame:
        """
        Kompletní pipeline: OCR + čištění + spojování.
        """
        if image is not None:
            self.image = image

        df = self.engine.process_data(self.image)
        preview = OcrPreviewQt(df, self.image)
        preview.show_and_wait()

        df = self._clean_text(df)

        df = self._remove_noise(df)

        df = self.merge_words(df)
        preview = OcrPreviewQt(df, self.image)
        preview.show_and_wait()

        df = self._remove_non_text(df)
        preview = OcrPreviewQt(df, self.image)
        preview.show_and_wait()

        return df



    @staticmethod
    def _clean_text(df: pd.DataFrame) -> pd.DataFrame:
        """
        Čistí a normalizuje texty v DataFrame.
        """
        df_clean = df.copy()

        # Unicode normalizace
        df_clean["text"] = df_clean["text"].apply(
            lambda x: unicodedata.normalize("NFC", str(x) if pd.notna(x) else "")
        )

        # Odstranění neviditelných znaků
        df_clean["text"] = df_clean["text"].apply(
            lambda x: ''.join(ch for ch in x if ch.isprintable() or ch.isspace())
        )

        # Odstranění přebytečných bílých znaků na začátku a konci
        df_clean["text"] = df_clean["text"].str.strip()

        return df_clean

    @staticmethod
    def _remove_noise(df: pd.DataFrame, confidence_threshold: float = 20.0) -> pd.DataFrame:
        """
        Odstraní šum z OCR výsledků podle minimálního prahu spolehlivosti, pouze pro řádky kde level == 5.
        """
        df_filtered = df.copy()
        mask = (df_filtered['level'] == 5)
        # Pro level==5 aplikujeme filtr na conf, ostatní ponecháme
        df_filtered = df_filtered[~mask | (df_filtered['conf'] >= confidence_threshold)]
        return df_filtered

    @staticmethod
    def _remove_non_text(df: pd.DataFrame) -> pd.DataFrame:
        """
        Odstraní řádky, které neobsahují žádný text (např. obrázky, tabulky, atd.)
        """
        df = df.copy()
        df = df[df['text'].notna() & (df['text'] != '') & (df['text'] != 'nan')]
        return df

    # --- spojování slov ---
    @staticmethod
    def _should_merge_words(cur_text: str, nxt_text: str) -> bool:
        cur_text = cur_text.strip()
        nxt_text = nxt_text.strip()

        # 1. končí : nebo ;
        if cur_text.endswith((':', ';')):
            return False

        # 2. aktuální je číslo a následující obsahuje písmena
        if re.fullmatch(r"\d+", cur_text) and re.search(r"[A-Za-zÁ-ž]", nxt_text):
            return False

        # 3. aktuální nemá číslice a následující obsahuje číslice
        if not re.search(r"\d", cur_text) and re.search(r"\d", nxt_text):
            return False

        return True

    @staticmethod
    def merge_words(df: pd.DataFrame, gap_threshold: float = 0.8) -> pd.DataFrame:
        """
        Přijme DataFrame z tesseractu (např. výstup pytesseract.image_to_data).
        Spojí všechna slova, která leží na téže řádce (stejné group keys) podle
        pravidel z _should_merge_words a poměru mezery k výšce řádku.
        gap_threshold: poměr mezery k výšce řádku (default 0.8)
        Sloučené texty budou mít level = 5. Conf se ignoruje.
        """
        if df is None or df.empty:
            return pd.DataFrame()

        # určíme klíče pro identifikaci řádků (pokud existují)
        row_keys = [k for k in ("page_num", "block_num", "par_num", "line_num") if k in df.columns]
        if not row_keys:
            # fallback: pokud nejsou tyto sloupce, považuj celý DataFrame za jednu "řádku"
            groups = [("", df.copy())]
        else:
            groups = list(df.groupby(row_keys))

        merged_results: list[dict] = []

        for _, group in groups:
            # pořadí podle left (a případně top pro stabilitu)
            words = group.copy()
            words["text"] = words["text"].fillna("").astype(str)
            words_sorted = words.sort_values(by=["left"]).to_dict(orient="records")

            if not words_sorted:
                continue

            # inicializujeme první slovo
            current = words_sorted[0].copy()
            current["text"] = str(current.get("text", ""))
            current["left"] = int(current.get("left", 0))
            current["top"] = int(current.get("top", 0))
            current["width"] = int(current.get("width", 0))
            current["height"] = int(current.get("height", 0))

            for nxt in words_sorted[1:]:
                nxt_entry = nxt.copy()
                nxt_entry["text"] = str(nxt_entry.get("text", ""))
                nxt_entry["left"] = int(nxt_entry.get("left", 0))
                nxt_entry["top"] = int(nxt_entry.get("top", 0))
                nxt_entry["width"] = int(nxt_entry.get("width", 0))
                nxt_entry["height"] = int(nxt_entry.get("height", 0))

                current_x1 = current["left"] + current["width"]
                next_x0 = nxt_entry["left"]
                gap = next_x0 - current_x1

                # výška řádku je maximální výška ze současného a následujícího slova
                line_height = max(current["height"], nxt_entry["height"])
                gap_ratio = gap / line_height if line_height > 0 else float('inf')

                if gap_ratio <= gap_threshold and OcrPipeline._should_merge_words(current["text"], nxt_entry["text"]):
                    # sloučíme
                    new_left = min(current["left"], nxt_entry["left"])
                    new_top = min(current["top"], nxt_entry["top"])
                    new_right = max(current_x1, nxt_entry["left"] + nxt_entry["width"])
                    new_bottom = max(current["top"] + current["height"], nxt_entry["top"] + nxt_entry["height"])

                    # spojení textu se single space, aktualizace bbox
                    current["text"] = (current["text"] + " " + nxt_entry["text"]).strip()
                    current["left"] = new_left
                    current["top"] = new_top
                    current["width"] = new_right - new_left
                    current["height"] = new_bottom - new_top
                    current["level"] = 5  # sloučené texty mají level 5
                else:
                    # přidáme aktuální a začneme nový
                    current["level"] = 5  # sloučené texty mají level 5
                    merged_results.append(current)
                    current = nxt_entry

            # přidáme poslední
            current["level"] = 5  # sloučené texty mají level 5
            merged_results.append(current)

        # převedeme zpět na DataFrame
        if merged_results:
            result_df = pd.DataFrame(merged_results)
            # zachováme původní sloupce, pokud existují
            for col in df.columns:
                if col not in result_df.columns:
                    result_df[col] = None
            return result_df
        else:
            return pd.DataFrame()
