import sys
import pandas as pd
import numpy as np
import cv2

# Podmíněný import PyQt5 a pyqtgraph
try:
    from PyQt5 import QtWidgets, QtCore, QtGui
    import pyqtgraph as pg
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    # Vytvoříme dummy třídy pro případ, že PyQt5 není dostupné
    class QtWidgets:
        class QWidget:
            pass
        class QApplication:
            @staticmethod
            def font():
                class Font:
                    def pointSize(self):
                        return 12
                    def setPointSize(self, size):
                        pass
                return Font()
    class QtCore:
        class Qt:
            LeftButton = 1
    class pg:
        @staticmethod
        def mkPen(color, width=1):
            return None
        @staticmethod
        def mkBrush(*args):
            return None
        class RectROI:
            def __init__(self, *args, **kwargs):
                pass
            def setPen(self, pen):
                pass
        class TextItem:
            def __init__(self, *args, **kwargs):
                pass
            def setFont(self, font):
                pass
            def setPos(self, x, y):
                pass
            def setText(self, text):
                pass
            def setVisible(self, visible):
                pass
        class GraphicsLayoutWidget:
            def addViewBox(self):
                return pg.ViewBox()
        class ViewBox:
            def setAspectLocked(self, locked):
                pass
            def setMouseEnabled(self, x=True, y=True):
                pass
            def setAcceptedMouseButtons(self, buttons):
                pass
            def addItem(self, item):
                pass
            def viewRect(self):
                class Rect:
                    def height(self):
                        return 100
                return Rect()
            def mapSceneToView(self, pos):
                class Point:
                    def x(self):
                        return 0
                    def y(self):
                        return 0
                return Point()
            sigXRangeChanged = None
            sigYRangeChanged = None
        class ImageItem:
            def __init__(self, image):
                pass
        class SignalProxy:
            def __init__(self, *args, **kwargs):
                pass

# Barvy podle labelu
LABEL_COLORS = {
    "Key": 'lime',
    "Value": 'blue',
    "Result": 'yellow',
    "Ignore": 'gray',
    None: 'g'
}

class OcrPreviewQt(QtWidgets.QWidget):
    def __init__(self, df: pd.DataFrame, image: cv2.Mat, hide_threshold=500):
        if not PYQT_AVAILABLE:
            print("⚠️  PyQt5 nebo pyqtgraph není dostupné. Preview nebude zobrazeno.")
            print("   Pro instalaci spusťte: pip install PyQt5 pyqtgraph")
            # Pouze provedeme konverzi souřadnic a vrátíme se
            self.df = df.copy()
            if "label" not in self.df.columns:
                self.df["label"] = None
            self._convert_coordinates()
            return

        # Zajistíme, že QApplication existuje
        self._ensure_qapplication()

        super().__init__()
        self.df = df.copy()
        if "label" not in self.df.columns:
            self.df["label"] = None

        # Uložíme původní rozměry pro konverzi souřadnic
        self.original_height = image.shape[0]
        self.original_width = image.shape[1]

        # Konverze souřadnic z Tesseract formátu (left, top, width, height)
        # na formát očekávaný preview (left, top, right, bottom)
        self._convert_coordinates()
        # Připravíme pg_* souřadnice hned po konverzi
        self._prepare_display_coordinates()

        # Konverze obrázku pro pyqtgraph - pouze BGR->RGB
        self.image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        self.hide_threshold = hide_threshold  # výška ViewBox pro skrytí textů
        self.init_ui()

    def _ensure_qapplication(self):
        """
        Zajistí, že QApplication existuje před vytvořením QWidget.
        """
        if PYQT_AVAILABLE:
            app = QtWidgets.QApplication.instance()
            if app is None:
                # Vytvoříme QApplication pokud neexistuje
                import sys
                self.app = QtWidgets.QApplication(sys.argv)
                print("✅ QApplication vytvořena pro OCR Preview")
            else:
                self.app = app

    def _convert_coordinates(self):
        """
        Konvertuje souřadnice z Tesseract formátu (left, top, width, height)
        na formát očekávaný preview (left, top, right, bottom).
        Také upravuje souřadnice pro pyqtgraph koordinátní systém.
        """
        # Pokud už máme right a bottom, nekonvertujeme
        if 'right' in self.df.columns and 'bottom' in self.df.columns:
            return

        # Pokud máme width a height, konvertujeme na right a bottom
        if 'width' in self.df.columns and 'height' in self.df.columns:
            self.df['right'] = self.df['left'] + self.df['width']
            self.df['bottom'] = self.df['top'] + self.df['height']
        else:
            # Fallback - pokud nemáme ani width/height ani right/bottom
            # Vytvoříme malé bounding boxy kolem pozice
            self.df['right'] = self.df['left'] + 50  # default width
            self.df['bottom'] = self.df['top'] + 20  # default height

    def _prepare_display_coordinates(self):
        """
        Připraví souřadnice pro vykreslování v pyqtgraph bez flipů.
        Převede Tesseract (left, top, right, bottom; původ s y dolů) do
        ViewBox soustavy s y nahoru: pg_top = H - bottom.
        """
        H = getattr(self, 'original_height', None)
        if H is None:
            return
        required = {'left', 'top', 'right', 'bottom'}
        if not required.issubset(self.df.columns):
            return
        self.df['pg_left'] = self.df['left']
        self.df['pg_right'] = self.df['right']
        self.df['pg_top'] = H - self.df['bottom']
        self.df['pg_bottom'] = H - self.df['top']
        self.df['pg_width'] = self.df['pg_right'] - self.df['pg_left']
        self.df['pg_height'] = self.df['pg_bottom'] - self.df['pg_top']

    def _make_qimage(self, img_rgb: np.ndarray) -> 'QtGui.QImage':
        """
        Vytvoří QImage bez změny orientace z numpy RGB.
        QImage očekává data v row-major, width x height, 3 kanály (RGB888).
        """
        h, w, c = img_rgb.shape
        bytes_per_line = 3 * w
        qimg = QtGui.QImage(img_rgb.data, w, h, bytes_per_line, QtGui.QImage.Format_RGB888)
        return qimg.copy()  # kopie, aby data žila mimo numpy buffer




    def _make_qpixmap(self, img_rgb: np.ndarray) -> 'QtGui.QPixmap':
        qimg = self._make_qimage(img_rgb)
        return QtGui.QPixmap.fromImage(qimg)





    def init_ui(self):
        layout = QtWidgets.QVBoxLayout()
        self.setLayout(layout)

        self.view = pg.GraphicsLayoutWidget()
        layout.addWidget(self.view)
        self.plot = self.view.addViewBox()
        self.plot.setAspectLocked(True)
        self.plot.setMouseEnabled(x=True, y=True)
        self.plot.setAcceptedMouseButtons(QtCore.Qt.LeftButton)

        # Obrázek z numpy RGB, zarovnán do soustavy s y nahoru (bez rotace dat)
        self.img_item = pg.ImageItem(self.image, axisOrder='row-major')
        # Použijeme transformaci pomocí setTransform, protože scale(sx, sy) nemusí být dostupné
        tr = QtGui.QTransform()
        tr.scale(1, -1)
        tr.translate(0, -self.original_height)
        self.img_item.setTransform(tr)
        # Pokud je stále zrcadlení podle svislé osy, zaměníme i X (zrcadlení podle X)
        # tr2 = QtGui.QTransform()
        # tr2.scale(-1, 1)
        # tr2.translate(-self.original_width, 0)
        # self.img_item.setTransform(tr2 * self.img_item.transform())

        self.plot.addItem(self.img_item)

        # Připravíme vykreslovací souřadnice (po konverzi right/bottom)
        self._prepare_display_coordinates()

        # Rects a texty
        self.rect_items = []
        self.text_items = []
        self.label_text_items = []
        for idx, row in self.df.iterrows():
            rect = pg.RectROI([row['pg_left'], row['pg_top']],
                              [row['pg_width'], row['pg_height']],
                              pen=pg.mkPen(LABEL_COLORS[None], width=2), movable=False)
            self.plot.addItem(rect)
            self.rect_items.append((rect, idx))

            text_item = pg.TextItem(str(row['text']), color='w', anchor=(0,1),
                                    border='k', fill=pg.mkBrush(0,0,0,150))
            font = QtWidgets.QApplication.font()
            font.setPointSize(int(font.pointSize() * 0.6))
            text_item.setFont(font)
            text_item.setPos(row['pg_left'], row['pg_top'] - 2)
            self.plot.addItem(text_item)
            self.text_items.append(text_item)

            label_text_item = pg.TextItem("", color='k', anchor=(0,0),
                                          border='w', fill=pg.mkBrush(255,255,255,150))
            font = QtWidgets.QApplication.font()
            font.setPointSize(int(font.pointSize() * 0.6))
            text_item.setFont(font)
            label_text_item.setPos(row['pg_left'], row['pg_bottom'] + 2)
            self.plot.addItem(label_text_item)
            self.label_text_items.append(label_text_item)

        # kliknutí
        self.proxy = pg.SignalProxy(self.plot.scene().sigMouseClicked, rateLimit=60, slot=self.on_click)

        # sledování zoomu pro skrývání textů
        self.plot.sigXRangeChanged.connect(self.on_view_changed)
        self.plot.sigYRangeChanged.connect(self.on_view_changed)

        self.setWindowTitle("OCR Preview")
        self.resize(800, 600)
        self.show()

    def on_view_changed(self):
        view_rect = self.plot.viewRect()
        scale_factor = view_rect.height()
        show_text = scale_factor < self.hide_threshold
        for t in self.text_items + self.label_text_items:
            t.setVisible(show_text)

    def on_click(self, event):
        mouse_event = event[0]
        if mouse_event.button() != QtCore.Qt.LeftButton:
            return
        pos = mouse_event.scenePos()
        mouse_point = self.plot.mapSceneToView(pos)
        x, y = mouse_point.x(), mouse_point.y()

        for rect, idx in self.rect_items:
            x0, y0 = rect.pos()
            w, h = rect.size()
            if x0 <= x <= x0 + w and y0 <= y <= y0 + h:
                self.show_dropdown(idx)
                break

    def show_dropdown(self, idx):
        options = ["Key", "Value", "Result", "Ignore"]
        item_text = self.df.at[idx, "text"]

        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle(f"Anotace: {item_text}")
        layout = QtWidgets.QVBoxLayout()
        dialog.setLayout(layout)

        combo = QtWidgets.QComboBox()
        combo.addItems(options)
        layout.addWidget(combo)
        combo.setCurrentText("Ignore" if self.df.at[idx, "label"] is None else self.df.at[idx, "label"])

        btn = QtWidgets.QPushButton("OK")
        layout.addWidget(btn)

        def confirm():
            choice = combo.currentText()
            self.df.at[idx, "label"] = choice
            rect, _ = self.rect_items[idx]
            rect.setPen(pg.mkPen(LABEL_COLORS[choice], width=2))
            # aktualizace textu labelu
            self.label_text_items[idx].setText(choice)
            print(f"Node '{item_text}' anotován jako: {choice}")
            dialog.accept()

        btn.clicked.connect(confirm)
        dialog.exec_()

    def show(self):
        """
        Zobrazí preview okno, ale pouze pokud je PyQt5 dostupné.
        """
        if PYQT_AVAILABLE:
            super().show()
            print("📱 OCR Preview zobrazeno. Zavřete okno pro pokračování.")
        else:
            print("⚠️  PyQt5 není dostupné - preview nelze zobrazit.")

    def show_and_wait(self):
        """
        Zobrazí preview okno a čeká na jeho zavření.
        Užitečné pro interaktivní použití.
        """
        if PYQT_AVAILABLE:
            self.show()
            # Spustíme event loop pouze pokud jsme vytvořili vlastní QApplication
            if hasattr(self, 'app') and self.app is not None:
                try:
                    self.app.exec_()
                except AttributeError:
                    # Fallback pro starší verze PyQt5
                    self.app.exec()
        else:
            print("⚠️  PyQt5 není dostupné - preview nelze zobrazit.")


# --- příklad použití ---
if __name__ == "__main__":
    data = [
        {"text": "Celkem", "left": 50, "top": 100, "right": 120, "bottom": 120},
        {"text": "1000",   "left": 150, "top": 100, "right": 200, "bottom": 120},
    ]
    df = pd.DataFrame(data)

    img = 255 * np.ones((300, 400, 3), dtype=np.uint8)

    app = QtWidgets.QApplication(sys.argv)
    preview = OcrPreviewQt(df, img)
    sys.exit(app.exec_())
