#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API count_nonzero_dim_IntList {
  using schema = at::Tensor (const at::Tensor &, at::IntArrayRef);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::count_nonzero";
  static constexpr const char* overload_name = "dim_IntList";
  static constexpr const char* schema_str = "count_nonzero.dim_IntList(Tensor self, int[] dim) -> Tensor";
  static at::Tensor call(const at::Tensor & self, at::IntArrayRef dim);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::IntArrayRef dim);
};

struct TORCH_API count_nonzero {
  using schema = at::Tensor (const at::Tensor &, ::std::optional<int64_t>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::count_nonzero";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "count_nonzero(Tensor self, int? dim=None) -> Tensor";
  static at::Tensor call(const at::Tensor & self, ::std::optional<int64_t> dim);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, ::std::optional<int64_t> dim);
};

struct TORCH_API count_nonzero_dim_IntList_out {
  using schema = at::Tensor & (const at::Tensor &, at::IntArrayRef, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::count_nonzero";
  static constexpr const char* overload_name = "dim_IntList_out";
  static constexpr const char* schema_str = "count_nonzero.dim_IntList_out(Tensor self, int[] dim, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, at::IntArrayRef dim, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, at::IntArrayRef dim, at::Tensor & out);
};

struct TORCH_API count_nonzero_out {
  using schema = at::Tensor & (const at::Tensor &, ::std::optional<int64_t>, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::count_nonzero";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "count_nonzero.out(Tensor self, int? dim=None, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, ::std::optional<int64_t> dim, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, ::std::optional<int64_t> dim, at::Tensor & out);
};

}} // namespace at::_ops
