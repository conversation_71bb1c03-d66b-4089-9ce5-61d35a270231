#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _coalesced_ {
  using schema = at::Tensor & (at::Tensor &, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_coalesced_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_coalesced_(Tensor(a!) self, bool coalesced) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, bool coalesced);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, bool coalesced);
};

struct TORCH_API _coalesced_out {
  using schema = at::Tensor & (const at::Tensor &, bool, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_coalesced";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "_coalesced.out(Tensor self, bool coalesced, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, bool coalesced, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, bool coalesced, at::Tensor & out);
};

struct TORCH_API _coalesced {
  using schema = at::Tensor (const at::Tensor &, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_coalesced";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_coalesced(Tensor self, bool coalesced) -> Tensor";
  static at::Tensor call(const at::Tensor & self, bool coalesced);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, bool coalesced);
};

}} // namespace at::_ops
