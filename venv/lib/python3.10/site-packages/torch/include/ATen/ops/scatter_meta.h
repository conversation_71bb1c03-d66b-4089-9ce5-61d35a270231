#pragma once

// @generated by torchgen/gen.py from NativeMetaFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/TensorIterator.h>
#include <ATen/TensorMeta.h>
#include <tuple>
#include <vector>

namespace at {
namespace meta {

struct TORCH_API structured_scatter_src : public at::impl::MetaBase {


    void meta(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src);
};
struct TORCH_API structured_scatter_value : public at::impl::MetaBase {


    void meta(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Scalar & value);
};
struct TORCH_API structured_scatter_reduce : public at::impl::MetaBase {


    void meta(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce);
};
struct TORCH_API structured_scatter_value_reduce : public at::impl::MetaBase {


    void meta(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Scalar & value, c10::string_view reduce);
};

} // namespace native
} // namespace at
