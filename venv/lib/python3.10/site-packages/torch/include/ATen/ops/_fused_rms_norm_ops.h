#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _fused_rms_norm {
  using schema = at::Tensor (const at::Tensor &, int64_t, const at::Tensor &, double);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_fused_rms_norm";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_fused_rms_norm(Tensor input, int normalized_shape_ndim, Tensor weight, float eps) -> Tensor";
  static at::Tensor call(const at::Tensor & input, int64_t normalized_shape_ndim, const at::Tensor & weight, double eps);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & input, int64_t normalized_shape_ndim, const at::Tensor & weight, double eps);
};

}} // namespace at::_ops
