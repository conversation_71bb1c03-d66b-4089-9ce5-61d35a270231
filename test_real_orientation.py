#!/usr/bin/env python3
"""
Test orientace s reálným PDF dokumentem.
"""

import os
import sys
import pandas as pd
from pdf2image import convert_from_path
import numpy as np

# Přidáme cestu k OCR modulu
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_real_document_orientation():
    """
    Test orientace s reálným PDF dokumentem.
    """
    print("🧪 Test orientace s reálným dokumentem")
    print("=" * 40)
    
    # Najdeme první PDF soubor
    data_folder = "Data"
    if not os.path.exists(data_folder):
        print(f"❌ Složka {data_folder} neexistuje!")
        return False
    
    pdf_files = [f for f in os.listdir(data_folder) if f.lower().endswith('.pdf')]
    if not pdf_files:
        print(f"❌ Ve složce {data_folder} nejsou žádné PDF soubory!")
        return False
    
    pdf_path = os.path.join(data_folder, pdf_files[0])
    print(f"📄 Testujeme soubor: {pdf_path}")
    
    try:
        # Konvertujeme PDF na obrázek
        print("🔄 Konvertuji PDF na obrázek...")
        images = convert_from_path(pdf_path, dpi=150)  # Nižší DPI pro rychlejší test
        if not images:
            print("❌ Nepodařilo se konvertovat PDF!")
            return False
        
        image = np.array(images[0])
        height, width = image.shape[:2]
        print(f"✅ Obrázek načten: {width}x{height}")
        
        # Zpracujeme pomocí OCR
        from OCR.OcrEngine import OcrEngine
        
        engine = OcrEngine()
        df = engine.process_data(image)
        
        if df is None or df.empty:
            print("❌ OCR nevrátilo žádná data!")
            return False
        
        print(f"✅ OCR dokončeno: {len(df)} textových prvků")
        
        # Testujeme konverzi souřadnic
        from OCR.OcrPreview import OcrPreviewQt
        
        print("🔄 Testujeme konverzi souřadnic...")
        preview = OcrPreviewQt.__new__(OcrPreviewQt)
        preview.df = df.copy()
        preview.original_height = height
        
        if "label" not in preview.df.columns:
            preview.df["label"] = None
        
        # Původní souřadnice
        original_coords = preview.df[['text', 'top']].head(3).copy()
        print("\n📊 Původní Y souřadnice (první 3 texty):")
        for i, row in original_coords.iterrows():
            text = str(row['text'])[:20]
            print(f"  '{text}': top={row['top']}")
        
        # Konverze
        preview._convert_coordinates()
        preview._convert_coordinates_for_pyqtgraph()
        
        print(f"\n📊 Po konverzi pro pyqtgraph (výška: {height}):")
        for i, row in preview.df.head(3).iterrows():
            text = str(row['text'])[:20]
            print(f"  '{text}': top={row['top']}")
        
        # Ověříme, že se souřadnice změnily logicky
        changes_detected = False
        for i, row in preview.df.head(3).iterrows():
            original_top = original_coords.iloc[i]['top']
            new_top = row['top']
            
            # Po konverzi by měla být Y souřadnice "převrácená"
            if abs(new_top - (height - original_top)) < 50:  # Tolerujeme malé rozdíly kvůli height konverzi
                changes_detected = True
                break
        
        if changes_detected:
            print("✅ Souřadnice byly správně konvertovány pro pyqtgraph")
        else:
            print("⚠️  Konverze souřadnic může být nesprávná")
        
        print("\n🎯 Výsledek:")
        print("  • Obrázek se převrátí vertikálně (np.flipud)")
        print("  • Y souřadnice se konvertují pro pyqtgraph koordinátní systém")
        print("  • Preview by mělo zobrazovat obrázek ve správné orientaci")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Test orientace s reálným dokumentem")
    print("=" * 50)
    
    success = test_real_document_orientation()
    
    if success:
        print("\n🎉 Test prošel úspěšně!")
        print("✅ Orientace by měla být nyní správná")
        print("\n📝 Pro úplné ověření nainstalujte PyQt5:")
        print("   pip install PyQt5 pyqtgraph")
        print("   a spusťte OcrPipeline s reálným dokumentem")
    else:
        print("\n❌ Test selhal!")
        sys.exit(1)
