import torch

from GAT_DS.GATClassifier import InterpretableGATClassifier, calculate_node_dim, predict
from Image.image_processor import load_image
#from NER.NER_test import ner_predict
from OCR import ocr
from preview import preview_qt as preview
#from semantic_classifier import key_classify
from utils import utils
from utils.utils import postprocess

file_path = ('Data/F2.PDF')

GAT_CONFIDENCE_THRESHOLD = 0.4  # Minimální confidence pro kandid<PERSON>ty na klasifikaci
CONFIG = {
    'results_dir': '../Results',
    'device': 'cpu',
    'model_path': 'GAT_DS/training_results/best_model.pth'
}
device = CONFIG['device']
MAX_KEY_CLASS = len(utils.KEY_CLASS_REGISTRY)
MAX_VALUE_CLASS = len(utils.VALUE_CLASS_REGISTRY)
MAX_RESULT_CLASS = len(utils.RESULT_CLASS_REGISTRY) - 1

df = ocr.process(load_image(file_path))

# df = ner_predict(df)

#preview.show_document_qt(file_path, df, return_results=False)
#key_classify.do(df)
#preview.show_document_qt(file_path, df, return_results=False)
exit()
df = postprocess(df)
preview.show_document_qt(file_path, df, return_results=False)
node_in_dim = calculate_node_dim(MAX_KEY_CLASS, MAX_VALUE_CLASS)
model = InterpretableGATClassifier(
    node_in_dim=node_in_dim,
    output_dim=MAX_RESULT_CLASS,
    num_value_classes=MAX_VALUE_CLASS + 1
).to(device)

model.load_state_dict(torch.load(CONFIG['model_path'], map_location=device))

pred_classes, confidences = predict(model, df, device, MAX_KEY_CLASS, MAX_VALUE_CLASS, MAX_RESULT_CLASS)

confidence_threshold = GAT_CONFIDENCE_THRESHOLD

filtered_pred_classes = pred_classes.copy()

low_confidence_mask = confidences < confidence_threshold
filtered_pred_classes[low_confidence_mask] = 0

df['result_class'] = filtered_pred_classes
df['confidence'] = confidences

# Zajistíme unikátnost hodnot v result_class - ponecháme pouze tu s nejvyšší confidence
def ensure_unique_result_classes(df):
    """
    Zajistí, že každá hodnota result_class (kromě 0) se vyskytuje pouze jednou.
    Ponechá řádek s nejvyšší confidence, ostatní vynuluje.
    """
    # Pracujeme pouze s řádky kde result_class > 0
    non_zero_mask = df['result_class'] > 0
    if not non_zero_mask.any():
        return df

    # Projdeme každou unikátní hodnotu result_class (kromě 0)
    unique_classes = df[non_zero_mask]['result_class'].unique()

    for class_value in unique_classes:
        # Najdeme všechny řádky s touto hodnotou result_class
        class_mask = df['result_class'] == class_value
        class_rows = df[class_mask]

        if len(class_rows) > 1:
            # Najdeme řádek s nejvyšší confidence
            best_idx = class_rows['confidence'].idxmax()

            # Vynulujeme result_class u všech ostatních řádků s touto hodnotou
            for idx in class_rows.index:
                if idx != best_idx:
                    df.loc[idx, 'result_class'] = 0

            print(f"Třída {class_value}: ponechán řádek {best_idx} (confidence: {df.loc[best_idx, 'confidence']:.3f}), "
                  f"vynulováno {len(class_rows)-1} duplicitních řádků")

    return df

df = ensure_unique_result_classes(df)

interactive_elements = len(df[df['value_class'] > 0])
updated_df = preview.show_document_qt(file_path, df, return_results=True)

# Automatické uložení aktualizovaného DataFrame do CSV
if updated_df is not None:
    output_file = utils.export_filtered_results(updated_df, file_path)
    print(f"✅ Data automaticky uložena do: {output_file}")
else:
    print("❌ Žádná data k uložení - preview bylo zrušeno nebo se nepodařilo získat aktualizovaný DataFrame")
